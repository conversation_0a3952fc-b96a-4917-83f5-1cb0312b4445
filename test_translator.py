import unittest
from translator import Translator
import sys

class TestTranslator(unittest.TestCase):
    def setUp(self):
        self.translator = Translator(temperature=0.3)
        # Ensure stdout can handle UTF-8
        if sys.stdout.encoding != 'utf-8':
            sys.stdout.reconfigure(encoding='utf-8')
        
    def test_translation_with_context(self):
        # Test case 1: Single line with no context
        japanese_text = "「はい、そうですね。」"
        result1 = self.translator.translate(
            japanese_text=japanese_text,
            speaker="Sakura",
            context_before="",
            context_after=""
        )
        print("\nTest 1 - Single line:", flush=True)
        print(f"Input: {japanese_text}", flush=True)
        print(f"Output: {result1}", flush=True)

        # Test case 2: Line with context before
        japanese_text = "「どうしましょうか？」"
        context_before = "「今日は天気がいいですね。」"
        result2 = self.translator.translate(
            japanese_text=japanese_text,
            speaker="Sakura",
            context_before=context_before,
            context_after=""
        )
        print("\nTest 2 - With context before:", flush=True)
        print(f"Context before: {context_before}", flush=True)
        print(f"Input: {japanese_text}", flush=True)
        print(f"Output: {result2}", flush=True)

        # Test case 3: Line with context before and after
        japanese_text = "「そうですね。」"
        context_before = "「今日は寒いですね。」"
        context_after = "「コーヒーでも飲みませんか？」"
        result3 = self.translator.translate(
            japanese_text=japanese_text,
            speaker="Sakura",
            context_before=context_before,
            context_after=context_after
        )
        print("\nTest 3 - With context before and after:", flush=True)
        print(f"Context before: {context_before}", flush=True)
        print(f"Input: {japanese_text}", flush=True)
        print(f"Context after: {context_after}", flush=True)
        print(f"Output: {result3}", flush=True)

        # Test case 4: Incomplete sentence with continuation
        japanese_text = "「私は」"
        context_after = "「学生です。」"
        result4 = self.translator.translate(
            japanese_text=japanese_text,
            speaker="Sakura",
            context_before="",
            context_after=context_after
        )
        print("\nTest 4 - Incomplete sentence with continuation:", flush=True)
        print(f"Input: {japanese_text}", flush=True)
        print(f"Context after: {context_after}", flush=True)
        print(f"Output: {result4}", flush=True)

        # Verify that all translations maintain original formatting
        for result in [result1, result2, result3, result4]:
            self.assertTrue(result.count('「') == result.count('」'), 
                          "Japanese quotation marks should be balanced")
            self.assertFalse("Speaker:" in result, 
                           "Translation should not include 'Speaker:' text")

if __name__ == '__main__':
    unittest.main(verbosity=2)
