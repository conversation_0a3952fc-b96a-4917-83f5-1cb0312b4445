#!/usr/bin/env python3
"""
Test script to verify that dialogue lines are correctly grouped by sentence_group_id
while preserving line-by-line structure for dialogue presentation.

The key insight: dialogue lines should have the same sentence_group_id when they
form a single sentence, but each line should be preserved individually.
"""

import tempfile
import os
import csv
from ks_to_csv_parser import process_ks_file

def test_dialogue_grouping():
    """Test that multi-line dialogue gets the same sentence_group_id"""
    
    # Create a test .ks file with multi-line dialogue
    test_ks_content = """[ニュネルヌクス]
「裏切り者の魔剣よ……この恨み決して忘れん……小娘共々必ずや[r]
魔界へと堕とし粉々に砕いて死霊の餌にしてくれる……」[pcm]

[レーヴェ]
「今更何を…！」[pcm]

[narrator]
ニュネルヌクスが忌々し気に指さすのは、少女が手にした剣。[r]
…薄赤く輝く刀身が揺らめき、まるで意志あるかの如くニュネルヌクスを[r]
照り返す。[pcm]
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ks', delete=False, encoding='utf-8') as f:
        f.write(test_ks_content)
        temp_ks_path = f.name
    
    try:
        # Process the file
        ks_output, csv_output = process_ks_file(temp_ks_path)
        
        # Read the CSV and check grouping
        with open(csv_output, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
        
        print("Generated CSV rows:")
        print("Row ID | Speaker | Japanese Text | Sentence Group ID | Status")
        print("-" * 80)
        
        dialogue_groups = {}
        for row in rows:
            print(f"{row['row_id']:6} | {row['speaker']:12} | {row['japanese_text'][:40]:40} | {row['sentence_group_id']:17} | {row['sentence_status']}")
            
            # Group by speaker and sentence_group_id
            key = f"{row['speaker']}_{row['sentence_group_id']}"
            if key not in dialogue_groups:
                dialogue_groups[key] = []
            dialogue_groups[key].append(row)
        
        print("\nDialogue groups:")
        for group_key, group_rows in dialogue_groups.items():
            speaker, group_id = group_key.split('_', 1)
            print(f"\nGroup {group_key}:")
            for row in group_rows:
                print(f"  - {row['japanese_text']}")
        
        # Verify that the multi-line ニュネルヌクス dialogue has the same group ID
        nyunel_rows = [row for row in rows if row['speaker'] == 'ニュネルヌクス']
        if len(nyunel_rows) >= 2:
            first_group_id = nyunel_rows[0]['sentence_group_id']
            second_group_id = nyunel_rows[1]['sentence_group_id']
            
            print(f"\nニュネルヌクス dialogue analysis:")
            print(f"First line group ID: {first_group_id}")
            print(f"Second line group ID: {second_group_id}")
            
            if first_group_id == second_group_id:
                print("✅ SUCCESS: Multi-line dialogue correctly grouped!")
            else:
                print("❌ FAILURE: Multi-line dialogue has different group IDs!")
                return False
        else:
            print("❌ FAILURE: Expected at least 2 ニュネルヌクス dialogue lines!")
            return False
        
        # Clean up
        os.unlink(temp_ks_path)
        os.unlink(ks_output)
        os.unlink(csv_output)
        
        return True
        
    except Exception as e:
        print(f"Error during test: {e}")
        # Clean up on error
        if os.path.exists(temp_ks_path):
            os.unlink(temp_ks_path)
        return False

if __name__ == "__main__":
    print("Testing dialogue grouping...")
    success = test_dialogue_grouping()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!")
