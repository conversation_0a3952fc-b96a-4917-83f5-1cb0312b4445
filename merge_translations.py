import os
import csv
from config import WORK_FOLDER, OUTPUT_FOLDER, ENSP

def merge_translations(original_file):
    # Create output folder if it doesn't exist
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
    
    # Prepare file paths
    filename = os.path.basename(original_file)
    base_name, ext = os.path.splitext(filename)
    csv_file = os.path.join(WORK_FOLDER, f"{base_name}.csv")
    output_file = os.path.join(OUTPUT_FOLDER, filename)
    
    # Check if both files exist
    if not os.path.exists(csv_file):
        print(f"Warning: CSV file not found for {filename}")
        return None
    
    # Read CSV file and create ID-to-translation mapping with tags
    translations = {}

    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['english_translation']:  # Only include rows that have translations
                # Replace regular spaces with ENSP in the translation
                translation = row['english_translation'].replace(' ', ENSP)

                # Remove double quotes if speaker is narrator
                if row['speaker'] == 'narrator':
                    translation = translation.replace('"', '')

                # Combine translation with tags
                if row['tags']:
                    translation = translation + row['tags']
                translations[str(row['row_id'])] = translation
    
    # Process original file and replace translations
    with open(original_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    merged_lines = []
    for line in lines:
        line = line.rstrip()
        if ';ID:' in line:
            text, id_part = line.rsplit(';ID:', 1)
            id_part = id_part.strip()

            if id_part in translations:
                merged_lines.append(translations[id_part])
            else:
                merged_lines.append(text.strip())
        else:
            merged_lines.append(line)
    
    # Write the merged file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(merged_lines))
    
    return output_file

def process_all_files():
    """Process all .ks files in the WORK_FOLDER that have matching .csv files"""
    if not os.path.exists(WORK_FOLDER):
        print(f"Error: Work folder '{WORK_FOLDER}' does not exist")
        return

    # Get all .ks files in the work folder
    ks_files = [f for f in os.listdir(WORK_FOLDER) if f.endswith('.ks')]
    
    if not ks_files:
        print(f"No .ks files found in '{WORK_FOLDER}'")
        return

    processed_files = []
    for ks_file in ks_files:
        input_path = os.path.join(WORK_FOLDER, ks_file)
        try:
            output_file = merge_translations(input_path)
            if output_file:
                processed_files.append((ks_file, output_file))
                print(f"Processed '{ks_file}':")
                print(f"  Output saved to: {output_file}")
        except Exception as e:
            print(f"Error processing '{ks_file}': {str(e)}")

    print(f"\nProcessed {len(processed_files)} files")
    return processed_files

if __name__ == "__main__":
    process_all_files()

