#!/usr/bin/env python3
"""
Test script to verify the translation splitting logic works correctly.
"""

import csv
import os

def _split_translation(translation, num_parts):
    """Split translation into roughly equal parts based on sentence structure"""
    # Remove quotes for processing
    text = translation.strip('"')

    # Split on common sentence endings
    parts = []
    current_part = ""
    words = text.split()
    words_per_part = len(words) // num_parts

    for i, word in enumerate(words):
        current_part += word + " "

        # Check if we should split here
        if (i + 1) % words_per_part == 0 and len(parts) < num_parts - 1:
            # Try to find a natural break point
            if any(current_part.strip().endswith(end) for end in [',', '.', '!', '?', '~']):
                parts.append(f'"{current_part.strip()}"')
                current_part = ""
            else:
                # If no natural break, look ahead for one
                look_ahead = min(3, len(words) - i - 1)
                for j in range(look_ahead):
                    if any(words[i + j].endswith(end) for end in [',', '.', '!', '?', '~']):
                        parts.append(f'"{current_part.strip() + " " + " ".join(words[i+1:i+j+1])}"')
                        current_part = ""
                        i += j
                        break

    # Add remaining text as the last part
    if current_part:
        parts.append(f'"{current_part.strip()}"')

    # If we didn't get enough parts, split the last part
    while len(parts) < num_parts:
        last_part = parts[-1].strip('"')
        split_point = len(last_part) // 2
        parts[-1] = f'"{last_part[:split_point]}"'
        parts.append(f'"{last_part[split_point:]}"')

    return parts

def test_split_translation():
    """Test the _split_translation method with the problematic translation"""

    # The problematic translation that needs to be split into 2 parts
    full_translation = '"O treacherous demon blade… I shall never forget this grudge… Together with that little wench, I will surely drag you down to the netherworld, shatter you to pieces, and feed you to the wraiths…"'

    print("Original translation:")
    print(full_translation)
    print()

    # Test splitting into 2 parts
    split_parts = _split_translation(full_translation, 2)

    print("Split into 2 parts:")
    for i, part in enumerate(split_parts, 1):
        print(f"Part {i}: {part}")
    print()

    # Test splitting into 3 parts
    split_parts_3 = _split_translation(full_translation, 3)

    print("Split into 3 parts:")
    for i, part in enumerate(split_parts_3, 1):
        print(f"Part {i}: {part}")
    print()

    return split_parts

def test_csv_structure():
    """Test how the CSV structure would look with the new approach"""
    
    csv_file = "translation/wip/00_プロローグ.csv"
    
    print("Current CSV structure for sentence group 14:")
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['sentence_group_id'] == '14':
                print(f"Row {row['row_id']}: {row['japanese_text']}")
                print(f"  Translation: {row['english_translation']}")
                print()

if __name__ == "__main__":
    print("=== Testing Translation Splitting Logic ===")
    print()
    
    # Test the splitting function
    split_parts = test_split_translation()
    
    print("=== Current CSV Structure ===")
    print()
    
    # Show current CSV structure
    test_csv_structure()
    
    print("=== Expected Result After Fix ===")
    print("Row 41 should get: ", split_parts[0])
    print("Row 42 should get: ", split_parts[1])
