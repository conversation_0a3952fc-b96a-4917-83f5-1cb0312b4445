#!/usr/bin/env python3
"""
Test the fix by reprocessing the problematic dialogue section
"""

import os
import tempfile
from ks_to_csv_parser import process_ks_file

def test_fix():
    # Extract the problematic section from the original .ks file
    test_content = """[ニュネルヌクス]
「裏切り者の魔剣よ……この恨み決して忘れん……小娘共々必ずや[r]
　魔界へと堕とし粉々に砕いて死霊の餌にしてくれる……」[pcm]

[レーヴェ]
「今更何を…！」[pcm]
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ks', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_path = f.name
    
    try:
        # Process with our fixed parser
        ks_output, csv_output = process_ks_file(temp_path)
        
        # Read and display results
        print("Fixed CSV output:")
        with open(csv_output, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines):
                print(f"{i+1:2}: {line.rstrip()}")
        
        # Check the specific rows
        import csv
        with open(csv_output, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
        
        print("\nAnalysis of ニュネルヌクス dialogue:")
        nyunel_rows = [row for row in rows if row['speaker'] == 'ニュネルヌクス']
        
        for i, row in enumerate(nyunel_rows):
            print(f"Row {i+1}:")
            print(f"  Text: {row['japanese_text']}")
            print(f"  Group ID: {row['sentence_group_id']}")
            print(f"  Status: {row['sentence_status']}")
            print()
        
        if len(nyunel_rows) >= 2:
            if nyunel_rows[0]['sentence_group_id'] == nyunel_rows[1]['sentence_group_id']:
                print("✅ SUCCESS: Both dialogue lines have the same sentence_group_id!")
                return True
            else:
                print("❌ FAILURE: Dialogue lines have different sentence_group_ids")
                return False
        
        # Clean up
        os.unlink(temp_path)
        os.unlink(ks_output)
        os.unlink(csv_output)
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_fix()
